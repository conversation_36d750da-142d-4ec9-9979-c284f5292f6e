const WithdrawalAccount = require("../models/withdrawalAccount");
const { StatusCodes } = require("http-status-codes");
const CustomError = require("../errors");
const Paystack = require("paystack-sdk").Paystack;
const paystack = new Paystack(process.env.PAYSTACK_SECRET_KEY);

const createWithdrawalAccount = async (req, res) => {
  const { paymentMethod, cryptoDetails, bankDetails } = req.body;

  // Check if details are provided based on payment method
  if (paymentMethod === "crypto" && !cryptoDetails) {
    throw new CustomError.BadRequestError("Crypto details are required");
  }
  if (paymentMethod === "bank" && !bankDetails) {
    throw new CustomError.BadRequestError("Bank details are required");
  }

  // Create withdrawal account
  const withdrawalAccount = await WithdrawalAccount.create({
    user: req.user._id,
    paymentMethod,
    cryptoDetails,
    bankDetails,
    isVerified: true,
    status: "active",
    // If this is the first account, make it default
    isDefault: !(await WithdrawalAccount.exists({ user: req.user._id })),
  });

  res.status(StatusCodes.CREATED).json({ data: withdrawalAccount });
};

const getAllWithdrawalAccounts = async (req, res) => {
  const withdrawalAccounts = await WithdrawalAccount.find({
    user: req.user._id,
  });
  res.status(StatusCodes.OK).json({ data: withdrawalAccounts });
};

const getWithdrawalAccount = async (req, res) => {
  const { id: withdrawalAccountId } = req.params;
  const withdrawalAccount = await WithdrawalAccount.findOne({
    _id: withdrawalAccountId,
    user: req.user._id,
  });

  if (!withdrawalAccount) {
    throw new CustomError.NotFoundError("Withdrawal account not found");
  }

  res.status(StatusCodes.OK).json({ data: withdrawalAccount });
};

const updateWithdrawalAccount = async (req, res) => {
  const { id: withdrawalAccountId } = req.params;
  const { paymentMethod, cryptoDetails, bankDetails, isDefault } = req.body;

  const withdrawalAccount = await WithdrawalAccount.findOne({
    _id: withdrawalAccountId,
    user: req.user._id,
  });

  if (!withdrawalAccount) {
    throw new CustomError.NotFoundError("Withdrawal account not found");
  }

  // Update fields if provided
  if (paymentMethod) withdrawalAccount.paymentMethod = paymentMethod;
  if (cryptoDetails) withdrawalAccount.cryptoDetails = cryptoDetails;
  if (bankDetails) withdrawalAccount.bankDetails = bankDetails;
  if (typeof isDefault !== "undefined") withdrawalAccount.isDefault = isDefault;

  await withdrawalAccount.save();
  res.status(StatusCodes.OK).json({ data: withdrawalAccount });
};

const deleteWithdrawalAccount = async (req, res) => {
  const { id: withdrawalAccountId } = req.params;
  const withdrawalAccount = await WithdrawalAccount.findOne({
    _id: withdrawalAccountId,
    user: req.user._id,
  });

  if (!withdrawalAccount) {
    throw new CustomError.NotFoundError("Withdrawal account not found");
  }

  await withdrawalAccount.deleteOne();
  res.status(StatusCodes.OK).json({ msg: "Withdrawal account removed" });
};

const setDefaultWithdrawalAccount = async (req, res) => {
  const { id: withdrawalAccountId } = req.params;

  const withdrawalAccount = await WithdrawalAccount.findOne({
    _id: withdrawalAccountId,
    user: req.user._id,
  });

  if (!withdrawalAccount) {
    throw new CustomError.NotFoundError("Withdrawal account not found");
  }

  withdrawalAccount.isDefault = true;
  await withdrawalAccount.save();

  res.status(StatusCodes.OK).json({ data: withdrawalAccount });
};

const verifyBankDetails = async (req, res) => {
  const { accountNumber, bankCode } = req.body;
  const resolve = await paystack.verification.resolveAccount({
    account_number: accountNumber,
    bank_code: bankCode,
  });
  res.status(StatusCodes.OK).json({ data: resolve });
};

module.exports = {
  createWithdrawalAccount,
  getAllWithdrawalAccounts,
  getWithdrawalAccount,
  updateWithdrawalAccount,
  deleteWithdrawalAccount,
  setDefaultWithdrawalAccount,
  verifyBankDetails,
};
