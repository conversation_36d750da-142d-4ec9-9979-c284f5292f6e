const express = require("express");
const router = express.Router();
const authentication = require("../middlewares/authentication");

const {
  createWithdrawalAccount,
  getAllWithdrawalAccounts,
  getWithdrawalAccount,
  updateWithdrawalAccount,
  deleteWithdrawalAccount,
  setDefaultWithdrawalAccount,
  verifyBankDetails,
} = require("../controllers/withdrawalAccountController");

router.use(authentication);

router.route("/").post(createWithdrawalAccount).get(getAllWithdrawalAccounts);

router
  .route("/:id")
  .get(getWithdrawalAccount)
  .patch(updateWithdrawalAccount)
  .delete(deleteWithdrawalAccount);

router.patch("/:id/set-default", setDefaultWithdrawalAccount);
router.post("/verify-bank-details", verifyBankDetails);

module.exports = router;
