import React from "react";
import {
  Fa<PERSON>heck,
  FaUser<PERSON>riends,
  FaDollarSign,
  FaPen,
  FaInfoCircle,
} from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { withdrawalAccountService } from "../services/api";

export default function AccountSetupStatus({ user }) {
  const navigate = useNavigate();

  // Fetch withdrawal accounts to check payment setup
  const { data: withdrawalAccountsData } = useQuery({
    queryKey: ["withdrawalAccounts"],
    queryFn: async () => {
      try {
        const response =
          await withdrawalAccountService.getAllWithdrawalAccounts();
        return response.data.withdrawalAccounts;
      } catch (error) {
        console.error("Error fetching withdrawal accounts:", error);
        return [];
      }
    },
    enabled: !!user, // Only fetch if user exists
  });

  const hasWithdrawalAccount =
    withdrawalAccountsData && withdrawalAccountsData.length > 0;

  // Calculate setup completion status
  const setupSteps = [
    {
      id: "profile_basic",
      title: "Complete Profile",
      description: "Add your avatar, display name, and bio",
      icon: FaUserFriends,
      completed: user?.avatar && user?.displayName && user?.about,
      required: true,
      action: () => navigate("/creator/account"),
      actionText:
        user?.avatar && user?.displayName && user?.about
          ? "Edit Profile"
          : "Complete Profile",
    },
    {
      id: "payment_setup",
      title: "Payment Details",
      description: "Set up payment method to receive earnings",
      icon: FaDollarSign,
      completed: hasWithdrawalAccount,
      required: true,
      action: () => navigate("/creator/payment-details"),
      actionText: hasWithdrawalAccount
        ? "Manage Payment Methods"
        : "Add Payment Method",
    },
    {
      id: "pricing_plans",
      title: "Subscription Plans",
      description: "Create subscription plans for your content",
      icon: FaDollarSign,
      completed: user?.pricingPlans?.length > 0,
      required: true,
      action: () => navigate("/creator/subscription-plans"),
      actionText:
        user?.pricingPlans?.length > 0 ? "Edit Plans" : "Create Plans",
    },
    {
      id: "first_post",
      title: "Create First Post",
      description: "Share your first piece of content",
      icon: FaPen,
      completed: user?.totalPosts > 0,
      required: false,
      action: () => navigate("/creator/posts/new"),
      actionText: user?.totalPosts > 0 ? "Create Another" : "Create Post",
    },
  ];

  // Calculate progress
  const requiredSteps = setupSteps.filter((step) => step.required);
  const completedRequiredSteps = requiredSteps.filter((step) => step.completed);
  const totalSteps = setupSteps.length;
  const completedSteps = setupSteps.filter((step) => step.completed);

  const requiredProgress =
    (completedRequiredSteps.length / requiredSteps.length) * 100;
  const overallProgress = (completedSteps.length / totalSteps) * 100;

  // Check if all required steps are completed
  const allRequiredCompleted =
    completedRequiredSteps.length === requiredSteps.length;

  // Don't show if all required steps are completed
  if (allRequiredCompleted) {
    return null;
  }

  return (
    <div className="bg-white rounded-2xl shadow-sm p-6 mb-6 border border-gray-100">
      {/* Header with Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-secondary">
            Complete Your Creator Setup
          </h2>
          <div className="flex items-center gap-3">
            <span className="text-sm text-gray-600 font-medium">
              {completedSteps.length}/{totalSteps} completed
            </span>
            <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-primary to-primary-400 transition-all duration-500 ease-out"
                style={{ width: `${overallProgress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Progress Summary */}
        <div className="flex items-center gap-6 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-primary rounded-full" />
            <span className="text-gray-600">
              Required: {completedRequiredSteps.length}/{requiredSteps.length}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-secondary-300 rounded-full" />
            <span className="text-gray-600">
              Optional: {completedSteps.length - completedRequiredSteps.length}/
              {totalSteps - requiredSteps.length}
            </span>
          </div>
          {allRequiredCompleted && (
            <div className="flex items-center gap-2 text-success">
              <FaCheck className="w-3 h-3" />
              <span className="font-medium">Ready to earn!</span>
            </div>
          )}
        </div>
      </div>

      {/* Setup Steps */}
      <div className="space-y-4">
        {setupSteps.map((step, index) => {
          const IconComponent = step.icon;
          const isCompleted = step.completed;
          const isRequired = step.required;

          return (
            <div
              key={step.id}
              className={`flex flex-col sm:flex-row justify-between items-start p-4 rounded-xl border transition-all duration-200 ${
                isCompleted
                  ? "bg-success-100 border-success-200 hover:bg-success-100"
                  : isRequired
                  ? "bg-primary-100 border-primary-200 hover:bg-primary-100"
                  : "bg-gray-50 border-gray-200 hover:bg-gray-100"
              }`}
            >
              <div className="flex items-start gap-4 mb-3 sm:mb-0 flex-1">
                <div
                  className={`p-3 rounded-xl ${
                    isCompleted
                      ? "bg-success text-white"
                      : isRequired
                      ? "bg-primary text-white"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  {isCompleted ? (
                    <FaCheck className="w-4 h-4" />
                  ) : (
                    <IconComponent className="w-4 h-4" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-base font-semibold text-secondary">
                      {step.title}
                    </h3>
                    {isRequired && !isCompleted && (
                      <span className="text-xs bg-primary text-white px-2 py-1 rounded-full font-medium">
                        Required
                      </span>
                    )}
                    {isCompleted && (
                      <span className="text-xs bg-success text-white px-2 py-1 rounded-full font-medium">
                        Completed
                      </span>
                    )}
                    {!isRequired && !isCompleted && (
                      <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full font-medium">
                        Optional
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">{step.description}</p>
                </div>
              </div>

              <button
                onClick={step.action}
                className={`px-6 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                  isCompleted
                    ? "bg-success-100 text-success hover:bg-success-200 border border-success-200"
                    : isRequired
                    ? "bg-primary text-white hover:bg-primary-400 shadow-sm"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                {step.actionText}
              </button>
            </div>
          );
        })}
      </div>

      {/* Call to Action */}
      {!allRequiredCompleted && (
        <div className="mt-6 p-5 bg-gradient-to-r from-primary to-primary-400 rounded-2xl text-white">
          <div className="flex items-start gap-4">
            <div className="p-2 bg-white bg-opacity-20 rounded-xl">
              <FaInfoCircle className="w-5 h-5" />
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-1">
                Complete required steps to start earning
              </h4>
              <p className="text-sm opacity-90">
                Finish {requiredSteps.length - completedRequiredSteps.length}{" "}
                more required steps to activate your creator account and start
                receiving payments
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {allRequiredCompleted && (
        <div className="mt-6 p-5 bg-gradient-to-r from-success to-success-500 rounded-2xl text-white">
          <div className="flex items-start gap-4">
            <div className="p-2 bg-white bg-opacity-20 rounded-xl">
              <FaCheck className="w-5 h-5" />
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-1">
                Congratulations! Your creator account is ready
              </h4>
              <p className="text-sm opacity-90">
                You can now start creating content and earning money. Complete
                the optional steps to maximize your earning potential.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
