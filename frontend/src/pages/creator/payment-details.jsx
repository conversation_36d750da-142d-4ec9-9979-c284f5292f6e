import { useState, useEffect } from "react";
import { BsBank2 } from "react-icons/bs";
import { RiGlobalLine, RiBankCard2Line } from "react-icons/ri";
import { useAuth } from "../../hooks/useAuth";
import {
  banksService,
  withdrawalAccountService,
  walletService,
} from "../../services/api";
import SearchSelect from "../../components/SearchSelect";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import LoadingSpinner from "../../components/LoadingSpinner";
import DeleteModal from "../../components/DeleteModal";
import toast from "react-hot-toast";
import { fCurrency } from "../../utils/formatNumber";
export default function PaymentDetails() {
  const queryClient = useQueryClient();
  const [selectedMethod, setSelectedMethod] = useState("bank");
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [banks, setBanks] = useState([]);
  const [accountNumber, setAccountNumber] = useState("");
  const [accountName, setAccountName] = useState("");
  const [selectedBank, setSelectedBank] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isVerifyingAccount, setIsVerifyingAccount] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState(null);
  const { user } = useAuth();

  // fetch wallet
  const { data: wallet, isLoading: isWalletLoading } = useQuery({
    queryKey: ["wallet"],
    queryFn: async () => {
      const response = await walletService.getWallet();
      return response.data;
    },
  });

  // Fetch countries
  const { data: countries, isLoading: isCountriesLoading } = useQuery({
    queryKey: ["countries"],
    queryFn: async () => {
      const response = await banksService.getCountries();
      return response.data;
    },
  });

  // Fetch existing withdrawal accounts
  const { data: withdrawalAccounts, isLoading: isWithdrawalAccountsLoading } =
    useQuery({
      queryKey: ["withdrawalAccounts"],
      queryFn: async () => {
        const response =
          await withdrawalAccountService.getAllWithdrawalAccounts();
        return response.data;
      },
    });

  // Create withdrawal account mutation
  const createWithdrawalAccountMutation = useMutation({
    mutationFn: (data) =>
      withdrawalAccountService.createWithdrawalAccount(data),
    onSuccess: () => {
      queryClient.invalidateQueries(["withdrawalAccounts"]);
      toast.success("Payment method added successfully");
      resetForm();
    },
    onError: (error) => {
      toast.error(
        error.response?.data?.message || "Failed to add payment method"
      );
    },
  });

  // Update withdrawal account mutation
  const updateWithdrawalAccountMutation = useMutation({
    mutationFn: ({ id, data }) =>
      withdrawalAccountService.updateWithdrawalAccount(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries(["withdrawalAccounts"]);
      toast.success("Payment method updated successfully");
      resetForm();
    },
    onError: (error) => {
      toast.error(
        error.response?.data?.message || "Failed to update payment method"
      );
    },
  });

  // Delete withdrawal account mutation
  const deleteWithdrawalAccountMutation = useMutation({
    mutationFn: (id) => withdrawalAccountService.deleteWithdrawalAccount(id),
    onSuccess: () => {
      queryClient.invalidateQueries(["withdrawalAccounts"]);
      toast.success("Payment method removed successfully");
    },
    onError: (error) => {
      toast.error(
        error.response?.data?.message || "Failed to remove payment method"
      );
    },
  });

  // Set default withdrawal account mutation
  const setDefaultWithdrawalAccountMutation = useMutation({
    mutationFn: (id) =>
      withdrawalAccountService.setDefaultWithdrawalAccount(id),
    onSuccess: () => {
      queryClient.invalidateQueries(["withdrawalAccounts"]);
      toast.success("Default payment method updated");
    },
    onError: (error) => {
      toast.error(
        error.response?.data?.message ||
          "Failed to update default payment method"
      );
    },
  });

  const verifyBankDetails = async () => {
    try {
      setIsVerifyingAccount(true);
      setAccountName(""); // Clear previous account name

      const response = await banksService.verifyBankDetails(
        accountNumber,
        selectedBank.code
      );

      // Handle the response structure you provided
      if (response.status && response.data) {
        setAccountName(response.data.account_name);
        toast.success(response.message || "Account number resolved");
      } else if (response.data && response.data.account_name) {
        // Fallback for different response structure
        setAccountName(response.data.account_name);
      }

      return response.data;
    } catch (error) {
      toast.error("Failed to verify bank details");
      setAccountName(""); // Clear account name on error
      console.error(error);
    } finally {
      setIsVerifyingAccount(false);
    }
  };

  useEffect(() => {
    if (accountNumber.length === 10 && selectedBank) {
      verifyBankDetails();
    }
  }, [accountNumber, selectedBank]);

  // Set default method to bank when not editing
  useEffect(() => {
    if (!isEditing) {
      setSelectedMethod("bank");
    }
  }, [isEditing]);

  const resetForm = () => {
    setSelectedMethod("bank");
    setSelectedCountry(null);
    setSelectedBank(null);
    setAccountNumber("");
    setAccountName("");
    setIsVerifyingAccount(false);
    setIsEditing(false);
    setEditingAccount(null);
    setDeleteModalOpen(false);
    setAccountToDelete(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);

      // Validate bank details (crypto is removed)
      if (!selectedCountry || !selectedBank || !accountNumber || !accountName) {
        toast.error("Please fill in all bank details");
        return;
      }

      const paymentData = {
        paymentMethod: "bank",
        bankDetails: {
          country: {
            name: selectedCountry.name,
            iso_code: selectedCountry.iso_code,
          },
          bankName: selectedBank.name,
          bankCode: selectedBank.code,
          accountNumber,
          accountName,
        },
      };

      if (isEditing && editingAccount) {
        // Update existing account
        await updateWithdrawalAccountMutation.mutateAsync({
          id: editingAccount._id,
          data: paymentData,
        });
      } else {
        // Create new account
        await createWithdrawalAccountMutation.mutateAsync(paymentData);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteAccount = (account) => {
    setAccountToDelete(account);
    setDeleteModalOpen(true);
  };

  const confirmDeleteAccount = async () => {
    if (accountToDelete) {
      await deleteWithdrawalAccountMutation.mutateAsync(accountToDelete._id);
      setDeleteModalOpen(false);
      setAccountToDelete(null);
    }
  };

  const cancelDeleteAccount = () => {
    setDeleteModalOpen(false);
    setAccountToDelete(null);
  };

  const handleSetDefault = async (id) => {
    await setDefaultWithdrawalAccountMutation.mutateAsync(id);
  };

  const handleEditAccount = async (account) => {
    setIsEditing(true);
    setEditingAccount(account);
    setSelectedMethod("bank"); // Only bank accounts are supported now

    // Auto-populate form fields
    if (account.bankDetails) {
      setAccountNumber(account.bankDetails.accountNumber);
      setAccountName(account.bankDetails.accountName);

      // Find and set the country
      const country = countries?.find(
        (c) => c.name === account.bankDetails.country.name
      );
      if (country) {
        setSelectedCountry(country);
        // Load banks for the country
        try {
          const banksResponse = await banksService.getBanks(country.name);
          setBanks(banksResponse.data);

          // Find and set the bank
          const bank = banksResponse.data.find(
            (b) => b.code === account.bankDetails.bankCode
          );
          if (bank) {
            setSelectedBank(bank);
          }
        } catch (error) {
          console.error("Error loading banks:", error);
        }
      }
    }
  };

  const handleCancelEdit = () => {
    resetForm();
  };

  if (isCountriesLoading || isWithdrawalAccountsLoading || isWalletLoading) {
    return <LoadingSpinner size="lg" />;
  }

  // Custom render functions for the country select
  const renderCountryOption = (country) => (
    <div className="flex items-center gap-2">
      <span className="text-lg">{country.iso_code}</span>
      <span>{country.name}</span>
    </div>
  );

  const renderSelectedCountry = (country) => (
    <>
      <span className="text-lg">{country.iso_code}</span>
      <span>{country.name}</span>
    </>
  );

  const filterCountry = (country, query) =>
    country.name.toLowerCase().includes(query.toLowerCase()) ||
    country.iso_code.toLowerCase().includes(query.toLowerCase());

  const renderBankOption = (bank) => (
    <div className="flex items-center gap-2">
      <span className="text-lg">{bank.name}</span>
    </div>
  );

  const renderSelectedBank = (bank) => (
    <>
      <span className="text-lg">{bank.name}</span>
      <span>{bank.code}</span>
    </>
  );

  const filterBank = (bank, query) =>
    bank.name.toLowerCase().includes(query.toLowerCase()) ||
    bank.code.toLowerCase().includes(query.toLowerCase());

  const handleSelectBank = (bank) => {
    setSelectedBank(bank);
    setAccountName(""); // Clear account name when bank changes
    setIsVerifyingAccount(false); // Reset verification state
  };

  const handleSelectCountry = async (country) => {
    try {
      setSelectedCountry(country);
      const banks = await banksService.getBanks(country.name);
      setBanks(banks.data);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="max-w-4xl p-6">
      {/* Payout Settings Header */}
      <div className="border-l-4 border-primary pl-4 mb-8">
        <h2 className="text-2xl font-bold text-gray-800">Payout Settings</h2>
        <p className="text-gray-600 mt-2">
          All the earnings will be sent to below selected payout method
        </p>
      </div>

      {/* Available Balance Card */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-8 border border-gray-100">
        <div className="flex justify-between items-center">
          <div>
            <div className="flex items-center gap-4">
              <img
                src={user?.avatar}
                alt="Profile"
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <p className="text-gray-600">Available balance</p>
                <p className="text-2xl font-bold">
                  {fCurrency(wallet?.balance)}
                </p>
              </div>
            </div>
          </div>
          <button
            disabled={wallet?.balance <= 999}
            className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-pink-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Withdraw Now
          </button>
        </div>
      </div>

      {/* Existing Payment Methods */}
      {withdrawalAccounts && withdrawalAccounts.length > 0 ? (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">Your Payment Methods</h3>
          <div className="space-y-4">
            {withdrawalAccounts.map((account) => (
              <div
                key={account._id}
                className="bg-white rounded-lg p-4 border border-gray-200 flex items-center justify-between"
              >
                <div className="flex items-center gap-3">
                  <BsBank2 className="text-gray-700 text-2xl" />
                  <div>
                    <p className="font-medium">
                      {account.bankDetails.bankName}
                    </p>
                    <p className="text-sm text-gray-600">
                      {account.bankDetails.accountNumber} •{" "}
                      {account.bankDetails.accountName}
                    </p>
                    <p className="text-xs text-gray-500">
                      {account.bankDetails.country.name}
                    </p>
                  </div>
                  <div className="flex flex-col gap-1">
                    {account.isDefault && (
                      <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                        Default
                      </span>
                    )}
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        account.isVerified
                          ? "bg-blue-100 text-blue-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {account.isVerified ? "Verified" : "Pending Verification"}
                    </span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEditAccount(account)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Edit
                  </button>
                  {!account.isDefault && (
                    <button
                      onClick={() => handleSetDefault(account._id)}
                      className="text-sm text-primary hover:text-pink-600"
                    >
                      Set as Default
                    </button>
                  )}
                  <button
                    onClick={() => handleDeleteAccount(account)}
                    className="text-sm text-red-600 hover:text-red-800"
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="mb-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
            <BsBank2 className="text-blue-500 text-4xl mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              No Payment Methods Added
            </h3>
            <p className="text-blue-700 mb-4">
              Add a bank account to receive your earnings. You can add multiple
              accounts and set one as default.
            </p>
          </div>
        </div>
      )}

      {/* Add/Edit Payment Method Form */}
      <form onSubmit={handleSubmit}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">
            {isEditing ? "Edit Payment Method" : "Add New Payment Method"}
          </h3>
          {isEditing && (
            <button
              type="button"
              onClick={handleCancelEdit}
              className="text-sm text-gray-600 hover:text-gray-800"
            >
              Cancel Edit
            </button>
          )}
        </div>
        <div className="space-y-4">
          {/* Crypto */}
          {/* <div className="border rounded-lg p-4 hover:border-primary cursor-pointer">
            <label className="flex items-center space-x-3">
              <input
                type="radio"
                name="paymentMethod"
                value="crypto"
                checked={selectedMethod === "crypto"}
                onChange={(e) => setSelectedMethod(e.target.value)}
                className="form-radio text-primary"
              />
              <MdOutlineCurrencyBitcoin className="text-[#F7931A] text-2xl" />
              <span className="font-medium">Cryptocurrency</span>
            </label>
            {selectedMethod === "crypto" && (
              <div className="mt-4 pl-7 space-y-4">
                <p className="text-sm text-gray-600 mb-2">
                  Please enter your cryptocurrency wallet address and select the
                  network. Make sure to double-check the address before
                  submitting.
                </p>
                <div className="space-y-3">
                  <select
                    value={cryptoNetwork}
                    onChange={(e) => setCryptoNetwork(e.target.value)}
                    className="w-full max-w-md px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select Network</option>
                    <option value="ETH">Ethereum (ETH)</option>
                    <option value="BSC">Binance Smart Chain (BSC)</option>
                    <option value="MATIC">Polygon (MATIC)</option>
                    <option value="USDT">Tether (USDT)</option>
                  </select>
                  <input
                    type="text"
                    value={cryptoAddress}
                    onChange={(e) => setCryptoAddress(e.target.value)}
                    placeholder="Enter your wallet address"
                    className="w-full max-w-md px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
              </div>
            )}
          </div> */}

          {/* Bank Transfer */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-4">
              <BsBank2 className="text-gray-700 text-2xl" />
              <span className="font-medium">Bank Transfer Details</span>
            </div>
            <div>
              <p className="text-sm text-gray-600 mb-4">
                Please provide your bank account details for receiving payments.
                Ensure all information is accurate to avoid payment delays.
              </p>
              <div className="space-y-3">
                <SearchSelect
                  options={countries}
                  value={selectedCountry}
                  onChange={handleSelectCountry}
                  placeholder="Select Country"
                  searchPlaceholder="Search countries..."
                  renderOption={renderCountryOption}
                  renderSelected={renderSelectedCountry}
                  filterOption={filterCountry}
                  className="max-w-md"
                  icon={RiGlobalLine}
                />
                <SearchSelect
                  options={banks}
                  value={selectedBank}
                  onChange={handleSelectBank}
                  placeholder="Select Bank"
                  searchPlaceholder="Search banks..."
                  renderOption={renderBankOption}
                  renderSelected={renderSelectedBank}
                  filterOption={filterBank}
                  className="max-w-md"
                  icon={RiBankCard2Line}
                />
                <input
                  type="text"
                  name="accountNumber"
                  value={accountNumber}
                  onChange={(e) => {
                    setAccountNumber(e.target.value);
                    // Clear account name when account number changes
                    if (e.target.value.length !== 10) {
                      setAccountName("");
                      setIsVerifyingAccount(false);
                    }
                  }}
                  placeholder="Account Number"
                  className="w-full max-w-md px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <div className="relative w-full max-w-md">
                  <input
                    type="text"
                    name="accountName"
                    value={accountName}
                    onChange={(e) => setAccountName(e.target.value)}
                    placeholder={
                      isVerifyingAccount
                        ? "Resolving account..."
                        : "Account Holder Name"
                    }
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary pr-10"
                    readOnly={selectedMethod === "bank"}
                  />
                  {isVerifyingAccount && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="mt-8">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-fit px-5 bg-primary text-white py-3 rounded-lg hover:bg-pink-600 transition-colors font-medium ${
              isSubmitting ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {isSubmitting
              ? isEditing
                ? "Updating..."
                : "Adding..."
              : isEditing
              ? "Update Payment Method"
              : "Add Payment Method"}
          </button>
        </div>
      </form>
    </div>
  );
}
